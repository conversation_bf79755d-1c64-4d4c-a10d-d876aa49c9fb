#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import pandas as pd
from typing import Dict, List, Any, Optional


class SqlGenerator:
    """
    工具类，用于将Excel文件中的内容转换为MySQL INSERT SQL语句
    """
    
    def __init__(self):
        """初始化SqlGenerator"""
        # 表名映射
        self.table_mapping = {
            '1 Product Rec': 't_promotion_product',
            '2 Have Fun': 't_have_fun',
            '3 Accessory Rec': 't_recommend_accessory'
        }
    
    def _format_value(self, value, column_name: str, table_name: str) -> str:
        """
        格式化SQL值

        Args:
            value: 要格式化的值
            column_name: 列名
            table_name: 表名

        Returns:
            str: 格式化后的SQL值字符串
        """
        # 特殊处理link_in_text列，当无值时使用空字符串而不是NULL
        if column_name == 'link_in_text':
            if value is None or pd.isna(value) or pd.isnull(value) or value == '':
                return "''"  # 返回空字符串

        # 特殊处理create_by和update_by字段，条件性设置
        if column_name in ['create_by', 'update_by']:
            # 如果Excel中有值，使用原值（转换为数字类型）
            if not (value is None or pd.isna(value) or pd.isnull(value) or value == ''):
                try:
                    # 尝试转换为整数（去除引号，输出数字类型）
                    if isinstance(value, float) and value.is_integer():
                        return str(int(value))
                    return str(int(value))
                except (ValueError, TypeError):
                    # 如果无法转换为整数，使用默认值9999
                    return '9999'
            else:
                # 如果Excel中为空值，使用默认值9999
                return '9999'

        # 特殊字段处理
        # 时间字段设置为 now()，但仅当它们为空时
        if column_name in ['create_time', 'update_time'] and (value is None or pd.isna(value) or pd.isnull(value) or value == ''):
            return 'now()'
        
        # 检查是否为NULL值
        if value is None or pd.isna(value) or pd.isnull(value) or value == '':
            return 'NULL'
        
        # 根据表名和列名处理特定的数据类型
        # 1. t_promotion_product表: id、image_no和is_deleted为int，其余为字符串
        # 2. t_have_fun表: id和is_deleted为int，其余为字符串
        # 3. t_recommend_accessory表: id和is_deleted为int，其余为字符串
        
        int_columns = {
            't_promotion_product': ['id', 'image_no', 'is_deleted'],
            't_have_fun': ['id', 'is_deleted'],
            't_recommend_accessory': ['id', 'is_deleted']
        }
        
        # 检查当前列是否应作为整数处理
        if table_name in int_columns and column_name in int_columns[table_name]:
            # 尝试将值转换为整数
            try:
                # 如果值为小数，例如1.0，转换为整数1
                if isinstance(value, float) and value.is_integer():
                    return str(int(value))
                # 尝试直接转换为整数
                return str(int(value))
            except (ValueError, TypeError):
                # 如果无法转换为整数，则返回NULL
                return 'NULL'
        else:
            # 特殊处理：如果值是浮点数并且看起来像整数加小数点零（如1.0, 42.0等）
            if isinstance(value, float) and value.is_integer():
                # 移除".0"后缀，转为字符串
                value = str(int(value))
            
            # 字符串值，需要转义单引号并用单引号括起来
            return f"'{str(value).replace('\'', '\'\'')}'"
    
    def generate_insert_sql(self, df: pd.DataFrame, table_name: str) -> List[str]:
        """
        为DataFrame生成INSERT SQL语句

        Args:
            df: 包含数据的DataFrame
            table_name: 目标表名

        Returns:
            List[str]: INSERT SQL语句列表
        """
        sql_statements = []

        # 获取列名
        columns = df.columns.tolist()
        columns_str = ', '.join([f"`{col}`" for col in columns])

        # 为每行生成INSERT语句
        for _, row in df.iterrows():
            values = []
            for col in columns:
                values.append(self._format_value(row[col], col, table_name))

            values_str = ', '.join(values)
            # 在表名前添加iot_app.前缀
            sql = f"INSERT INTO `iot_app`.`{table_name}` ({columns_str}) VALUES ({values_str});"
            sql_statements.append(sql)

        return sql_statements

    def generate_update_sql(self, df: pd.DataFrame, table_name: str) -> List[str]:
        """
        为DataFrame生成UPDATE SQL语句（按id更新）

        Args:
            df: 包含数据的DataFrame
            table_name: 目标表名

        Returns:
            List[str]: UPDATE SQL语句列表
        """
        sql_statements = []

        # 获取列名
        columns = df.columns.tolist()

        # 检查是否有id列
        if 'id' not in columns:
            raise ValueError(f"表 {table_name} 的数据中缺少 'id' 列，无法生成UPDATE语句")

        # 为每行生成UPDATE语句
        for _, row in df.iterrows():
            # 获取id值
            id_value = self._format_value(row['id'], 'id', table_name)

            # 如果id为NULL，跳过这一行
            if id_value == 'NULL':
                print(f"警告: 跳过id为空的行")
                continue

            # 构建SET子句（排除id列）
            set_clauses = []
            for col in columns:
                if col != 'id':  # 不更新id列
                    formatted_value = self._format_value(row[col], col, table_name)
                    set_clauses.append(f"`{col}` = {formatted_value}")

            if set_clauses:  # 确保有要更新的列
                set_str = ', '.join(set_clauses)
                # 在表名前添加iot_app.前缀
                sql = f"UPDATE `iot_app`.`{table_name}` SET {set_str} WHERE `id` = {id_value};"
                sql_statements.append(sql)

        return sql_statements
    
    def process_excel_to_sql(self, excel_path: str, output_dir: str = None, sql_type: str = 'insert') -> Dict[str, Any]:
        """
        处理Excel文件并生成SQL语句

        Args:
            excel_path: Excel文件路径
            output_dir: 输出目录（如果提供，SQL将写入文件）
            sql_type: SQL语句类型，'insert' 或 'update'

        Returns:
            Dict: 处理结果统计
        """
        results = {
            'total_sheets': 0,
            'total_rows': 0,
            'sheets_info': {},
            'sql_files': [],
            'sql_type': sql_type
        }
        
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(excel_path)
            
            # 处理每个sheet
            for sheet_name in self.table_mapping:
                if sheet_name in excel_file.sheet_names:
                    table_name = self.table_mapping[sheet_name]
                    
                    # 读取sheet数据，强制将所有列读取为字符串类型，避免自动类型转换
                    df = pd.read_excel(
                        excel_file,
                        sheet_name=sheet_name,
                        dtype=str,  # 将所有列读取为字符串
                        keep_default_na=False  # 避免将空字段转换为NaN
                    )
                    row_count = len(df)

                    # 根据sql_type生成相应的SQL语句
                    if sql_type.lower() == 'update':
                        sql_statements = self.generate_update_sql(df, table_name)
                    else:
                        sql_statements = self.generate_insert_sql(df, table_name)
                    
                    # 更新统计信息
                    results['total_sheets'] += 1
                    results['total_rows'] += row_count
                    results['sheets_info'][sheet_name] = {
                        'table_name': table_name,
                        'rows': row_count,
                        'sql_count': len(sql_statements)
                    }
                    
                    # 如果提供了输出目录，则写入文件
                    if output_dir:
                        os.makedirs(output_dir, exist_ok=True)
                        # 根据SQL类型调整文件名
                        file_suffix = "_update" if sql_type.lower() == 'update' else "_insert"
                        file_path = os.path.join(output_dir, f"{table_name}{file_suffix}.sql")
                        
                        # 检查文件是否已存在
                        file_exists = os.path.exists(file_path)
                        mode = 'w'  # 使用'w'模式覆盖现有文件
                        
                        with open(file_path, mode, encoding='utf-8') as f:
                            sql_type_name = "UPDATE" if sql_type.lower() == 'update' else "INSERT"
                            f.write(f"-- 表 {table_name} 的{sql_type_name}语句 (来自 {sheet_name})\n")
                            f.write(f"-- 共 {row_count} 行数据\n")
                            f.write(f"-- 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            if sql_type.lower() == 'update':
                                f.write(f"-- 注意: UPDATE语句按id字段进行更新\n")
                            f.write("\n")
                            f.write('\n'.join(sql_statements))
                        
                        if file_exists:
                            print(f"覆盖现有文件: {file_path}")
                        else:
                            print(f"创建新文件: {file_path}")
                            
                        results['sql_files'].append(file_path)
            
        except Exception as e:
            print(f"处理Excel生成SQL时出错: {str(e)}")
        
        return results


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='将Excel数据转换为MySQL INSERT或UPDATE语句')
    parser.add_argument('--excel', required=True, help='Excel文件路径')
    parser.add_argument('--output-dir', default='sql_output', help='SQL输出目录')
    parser.add_argument('--sql-type', choices=['insert', 'update'], default='insert',
                       help='生成的SQL语句类型: insert (默认) 或 update')
    
    args = parser.parse_args()
    
    generator = SqlGenerator()
    results = generator.process_excel_to_sql(args.excel, args.output_dir, args.sql_type)
    
    print("\n处理结果摘要:")
    print(f"SQL类型: {results['sql_type'].upper()}")
    print(f"处理sheet数: {results['total_sheets']}")
    print(f"处理总行数: {results['total_rows']}")
    
    for sheet_name, info in results['sheets_info'].items():
        print(f"\n{sheet_name} -> {info['table_name']}:")
        print(f"  行数: {info['rows']}")
        print(f"  SQL语句数: {info['sql_count']}")
    
    if results['sql_files']:
        print("\n生成的SQL文件:")
        for file_path in results['sql_files']:
            print(f"  - {file_path}")
