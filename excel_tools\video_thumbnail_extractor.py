#!/usr/bin/env python3
import os
import cv2
import numpy as np
import argparse
from pathlib import Path

def process_image(frame):
    """处理图片：裁剪为正方形并添加圆角"""
    h, w = frame.shape[:2]
    
    # 如果是长图，以宽度为基准裁剪为正方形
    if h > w:
        # 从顶部开始裁剪
        frame = frame[:w, :]
    
    # 添加5dp圆角（假设1dp=1像素）
    h, w = frame.shape[:2]
    radius = 5
    
    # 创建圆角蒙版
    mask = np.zeros((h, w), dtype=np.uint8)
    cv2.rectangle(mask, (radius, 0), (w-radius, h), 255, -1)
    cv2.rectangle(mask, (0, radius), (w, h-radius), 255, -1)
    cv2.circle(mask, (radius, radius), radius, 255, -1)
    cv2.circle(mask, (w-radius, radius), radius, 255, -1)
    cv2.circle(mask, (radius, h-radius), radius, 255, -1)
    cv2.circle(mask, (w-radius, h-radius), radius, 255, -1)
    
    # 应用蒙版
    result = np.zeros_like(frame)
    result[mask == 255] = frame[mask == 255]
    
    return result

def extract_thumbnail(video_path, output_dir, capture_time=1.0):
    """从视频中提取封面图片"""
    try:
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            print(f"错误: 无法打开视频文件 {video_path}")
            return False
        
        # 获取视频总帧数和帧率
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        # 确保截取时间不超过视频长度
        capture_time = min(capture_time, duration - 0.1) if duration > 0.1 else 0
        
        # 设置到指定时间点
        frame_number = int(capture_time * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            print(f"错误: 无法读取视频帧 {video_path}")
            cap.release()
            return False
        
        # 处理图片：裁剪和圆角
        frame = process_image(frame)
        
        # 生成输出文件名
        video_name = video_path.stem
        output_filename = f"{video_name}.png"
        output_path = output_dir / output_filename
        
        # 保存图片（降低质量）
        success = cv2.imwrite(str(output_path), frame, [cv2.IMWRITE_PNG_COMPRESSION, 9])
        cap.release()
        
        if success:
            print(f"✓ 成功提取: {video_path.name} -> {output_filename}")
            return True
        else:
            print(f"错误: 保存图片失败 {output_path}")
            return False
            
    except Exception as e:
        print(f"错误: 处理 {video_path} 时发生异常: {e}")
        return False



def process_folder(folder_path, capture_time=1.0):
    """批量处理文件夹中的MP4文件"""
    folder_path = Path(folder_path)
    
    if not folder_path.exists():
        print(f"错误: 文件夹不存在 {folder_path}")
        return
    
    # 创建输出目录
    output_dir = folder_path.parent / "video_cover_page"
    output_dir.mkdir(exist_ok=True)
    
    # 查找所有MP4文件
    mp4_files = list(set(list(folder_path.glob("*.mp4")) + list(folder_path.glob("*.MP4"))))
    
    if not mp4_files:
        print("未找到MP4文件")
        return
    
    print(f"找到 {len(mp4_files)} 个MP4文件")
    print(f"输出目录: {output_dir}")
    print(f"截取时间点: {capture_time}秒")
    print("-" * 50)
    
    success_count = 0
    
    for i, video_file in enumerate(mp4_files, 1):
        print(f"[{i}/{len(mp4_files)}] 处理: {video_file.name}")
        
        if extract_thumbnail(video_file, output_dir, capture_time):
            success_count += 1
    
    print("-" * 50)
    print(f"处理完成! 成功: {success_count}/{len(mp4_files)}")
    print(f"封面图片保存在: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="批量视频封面提取工具")
    parser.add_argument("folder", help="包含MP4文件的文件夹路径")
    parser.add_argument("-t", "--time", type=float, default=1.0, 
                       help="截取时间点(秒), 默认1.0秒")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("批量视频封面提取工具")
    print("=" * 60)
    
    process_folder(args.folder, args.time)

if __name__ == "__main__":
    main()