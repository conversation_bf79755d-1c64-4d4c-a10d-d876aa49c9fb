#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import boto3
import sys
from typing import Dict, List, Any, Tuple
from datetime import datetime


class S3Uploader:
    """
    工具类，用于将Banner和HaveFun文件夹中的文件上传到AWS S3
    """

    def __init__(self, access_key: str, secret_key: str, region: str, bucket_name: str, s3_key_prefix: str = ""):
        """
        初始化S3Uploader
        
        Args:
            access_key: AWS访问密钥ID
            secret_key: AWS秘密访问密钥
            region: AWS区域
            bucket_name: S3桶名称
            s3_key_prefix: S3键前缀，所有上传文件的S3键将以此为前缀
        """
        self.access_key = access_key
        self.secret_key = secret_key
        self.region = region
        self.bucket_name = bucket_name
        self.s3_key_prefix = s3_key_prefix
        # 确保s3_key_prefix以斜杠结尾（如果非空）
        if self.s3_key_prefix and not self.s3_key_prefix.endswith('/'):
            self.s3_key_prefix += '/'
        self.s3_client = self._create_s3_client()
        # 用于记录S3键的使用情况，避免同名文件冲突
        self.used_s3_keys = set()

        
    def _create_s3_client(self):
        """创建S3客户端"""
        return boto3.client(
            's3',
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            region_name=self.region
        )
    
    def file_exists_in_s3(self, s3_key: str, debug: bool = False) -> bool:
        """
        检查文件是否已经存在于S3桶中

        Args:
            s3_key: S3中的对象键
            debug: 是否启用调试模式

        Returns:
            bool: 如果文件存在返回True，否则返回False
        """
        try:
            if debug:
                print(f"检查S3文件是否存在: s3://{self.bucket_name}/{s3_key}")

            # 使用head_object检查文件是否存在，该方法只获取对象的元数据，不下载文件内容
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)

            if debug:
                print(f"✅ 文件已存在于S3: s3://{self.bucket_name}/{s3_key}")
                print(f"   文件大小: {response.get('ContentLength', 'Unknown')} bytes")
                print(f"   最后修改: {response.get('LastModified', 'Unknown')}")

            return True
        except Exception as e:
            if debug:
                print(f"❌ 文件不存在于S3: s3://{self.bucket_name}/{s3_key}")
                print(f"   错误详情: {str(e)}")

            # 如果文件不存在，将会抛出异常
            return False
    
    def upload_file(self, file_path: str, s3_key: str, debug: bool = False) -> Tuple[bool, str]:
        """
        上传单个文件到S3，如果文件已存在则跳过

        Args:
            file_path: 本地文件路径
            s3_key: S3中的对象键
            debug: 是否启用调试模式

        Returns:
            tuple: (成功状态, 状态信息)，其中成功状态为True表示处理成功，状态信息为"uploaded"、"skipped"
        """
        if debug:
            print(f"\n--- 开始处理文件 ---")
            print(f"本地文件: {file_path}")
            print(f"目标S3键: {s3_key}")
            print(f"S3桶: {self.bucket_name}")

        # 先检查文件是否已存在
        if self.file_exists_in_s3(s3_key, debug):
            print(f"文件已存在，跳过上传: s3://{self.bucket_name}/{s3_key}")
            return True, "skipped"

        # 文件不存在，执行上传
        try:
            if debug:
                print(f"开始上传文件到S3...")

            self.s3_client.upload_file(file_path, self.bucket_name, s3_key)
            print(f"成功上传: {file_path} -> s3://{self.bucket_name}/{s3_key}")
            return True, "uploaded"
        except Exception as e:
            print(f"上传失败 {file_path}: {str(e)}")
            return False, "failed"
    
    def process_folders_and_upload(self, base_dir: str, debug: bool = True) -> Dict[str, Any]:
        """
        直接处理Banner和HaveFun文件夹并上传所有文件到S3

        Args:
            base_dir: 基础目录路径（包含Banner和HaveFun文件夹的目录）
            debug: 是否启用调试模式，默认为True

        Returns:
            Dict: 上传结果统计
        """
        results = {
            'total': 0,
            'success': 0,
            'skipped': 0,  # 已存在于S3的文件
            'failed': 0,
            'details': {
                'banner': {'total': 0, 'success': 0, 'skipped': 0, 'failed': 0},
                'havefun': {'total': 0, 'success': 0, 'skipped': 0, 'failed': 0}
            }
        }

        try:
            if debug:
                print(f"开始处理文件夹: {base_dir}")
                print(f"当前工作目录: {os.getcwd()}")
                print(f"基础目录的完整路径: {os.path.abspath(base_dir)}")
                print(f"基础目录是否存在: {os.path.exists(base_dir)}")
            
            # 处理Banner文件夹
            banner_dir = os.path.join(base_dir, 'Banner')
            if os.path.exists(banner_dir):
                if debug:
                    print(f"\n开始处理Banner文件夹: {banner_dir}")
                    print(f"Banner目录是否存在: {os.path.exists(banner_dir)}")

                # 遍历Banner文件夹下的所有文件
                banner_files = self._get_all_files_in_directory(banner_dir, debug)
                results['details']['banner']['total'] = len(banner_files)

                if debug:
                    print(f"Banner文件夹中找到 {len(banner_files)} 个文件")

                for file_path in banner_files:
                    # 获取文件相对于基础目录的完整路径
                    rel_path = os.path.relpath(file_path, base_dir)
                    # 转换为标准格式(使用正斜杠)
                    rel_path = rel_path.replace('\\', '/')
                    base_s3_key = self.s3_key_prefix + rel_path

                    if debug:
                        print(f"\n处理Banner文件: {rel_path}")

                    # 检查原始S3键是否存在
                    if self.file_exists_in_s3(base_s3_key, debug):
                        # 文件已存在，直接跳过
                        if debug:
                            print(f"文件已存在于S3，跳过上传: {base_s3_key}")
                        success, status = True, "skipped"
                        final_s3_key = base_s3_key
                    else:
                        # 文件不存在，使用原始键上传
                        final_s3_key = self._generate_unique_s3_key(base_s3_key)
                        if debug:
                            print(f"文件不存在，准备上传: {final_s3_key}")
                        success, status = self.upload_file(file_path, final_s3_key, debug)

                    # 统计结果
                    if success:
                        if status == "uploaded":
                            results['details']['banner']['success'] += 1
                        elif status == "skipped":
                            results['details']['banner']['skipped'] += 1
                    else:
                        results['details']['banner']['failed'] += 1
            else:
                if debug:
                    print(f"Banner文件夹不存在: {banner_dir}")
            
            # 处理HaveFun文件夹
            havefun_dir = os.path.join(base_dir, 'HaveFun')
            if os.path.exists(havefun_dir):
                if debug:
                    print(f"\n开始处理HaveFun文件夹: {havefun_dir}")
                    print(f"HaveFun目录是否存在: {os.path.exists(havefun_dir)}")

                # 遍历HaveFun文件夹下的所有文件
                havefun_files = self._get_all_files_in_directory(havefun_dir, debug)
                results['details']['havefun']['total'] = len(havefun_files)

                if debug:
                    print(f"HaveFun文件夹中找到 {len(havefun_files)} 个文件")

                for file_path in havefun_files:
                    # 获取文件相对于基础目录的完整路径
                    rel_path = os.path.relpath(file_path, base_dir)
                    # 转换为标准格式(使用正斜杠)
                    rel_path = rel_path.replace('\\', '/')
                    base_s3_key = self.s3_key_prefix + rel_path

                    if debug:
                        print(f"\n处理HaveFun文件: {rel_path}")

                    # 检查原始S3键是否存在
                    if self.file_exists_in_s3(base_s3_key, debug):
                        # 文件已存在，直接跳过
                        if debug:
                            print(f"文件已存在于S3，跳过上传: {base_s3_key}")
                        success, status = True, "skipped"
                        final_s3_key = base_s3_key
                    else:
                        # 文件不存在，使用原始键上传
                        final_s3_key = self._generate_unique_s3_key(base_s3_key)
                        if debug:
                            print(f"文件不存在，准备上传: {final_s3_key}")
                        success, status = self.upload_file(file_path, final_s3_key, debug)

                    # 统计结果
                    if success:
                        if status == "uploaded":
                            results['details']['havefun']['success'] += 1
                        elif status == "skipped":
                            results['details']['havefun']['skipped'] += 1
                    else:
                        results['details']['havefun']['failed'] += 1
            else:
                if debug:
                    print(f"HaveFun文件夹不存在: {havefun_dir}")
            
            # 更新总数统计
            results['success'] = results['details']['banner']['success'] + results['details']['havefun']['success']
            results['skipped'] = results['details']['banner']['skipped'] + results['details']['havefun']['skipped']
            results['failed'] = results['details']['banner']['failed'] + results['details']['havefun']['failed']
            results['total'] = results['success'] + results['skipped'] + results['failed']

        except Exception as e:
            print(f"处理文件夹和上传文件时出错: {str(e)}")

        return results

    def _get_all_files_in_directory(self, directory: str, debug: bool = False) -> List[str]:
        """
        获取目录及其子目录下的所有文件

        Args:
            directory: 要搜索的目录
            debug: 是否启用调试模式

        Returns:
            List[str]: 所有文件的完整路径列表
        """
        all_files = []

        if not os.path.exists(directory):
            if debug:
                print(f"目录不存在: {directory}")
            return all_files

        # 使用os.walk递归遍历目录
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                all_files.append(file_path)
                if debug:
                    rel_path = os.path.relpath(file_path, directory)
                    print(f"找到文件: {rel_path}")

        if debug:
            print(f"目录 {directory} 中总共找到 {len(all_files)} 个文件")

        return all_files

    def _generate_unique_s3_key(self, base_s3_key: str) -> str:
        """
        生成唯一的S3键，如果键已存在则添加时间戳后缀

        Args:
            base_s3_key: 基础S3键

        Returns:
            str: 唯一的S3键
        """
        if base_s3_key not in self.used_s3_keys:
            self.used_s3_keys.add(base_s3_key)
            return base_s3_key

        # 如果键已存在，添加时间戳后缀
        path_parts = base_s3_key.rsplit('.', 1)
        if len(path_parts) == 2:
            base_name, extension = path_parts
            timestamp = datetime.now().strftime("%Y%m%d%H")
            unique_key = f"{base_name}_{timestamp}.{extension}"
        else:
            timestamp = datetime.now().strftime("%Y%m%d%H")
            unique_key = f"{base_s3_key}_{timestamp}"

        # 递归检查，确保生成的键是唯一的
        if unique_key in self.used_s3_keys:
            # 如果时间戳后缀也重复，添加更精确的时间戳
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            if len(path_parts) == 2:
                base_name, extension = path_parts
                unique_key = f"{base_name}_{timestamp}.{extension}"
            else:
                unique_key = f"{base_s3_key}_{timestamp}"

        self.used_s3_keys.add(unique_key)
        return unique_key


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='上传Banner和HaveFun文件夹到S3')
    parser.add_argument('--base-dir', required=True, help='基础目录路径（包含Banner和HaveFun文件夹的目录）')
    parser.add_argument('--access-key', help='AWS访问密钥ID (可通过AWS_ACCESS_KEY_ID环境变量设置)')
    parser.add_argument('--secret-key', help='AWS秘密访问密钥 (可通过AWS_SECRET_ACCESS_KEY环境变量设置)')
    parser.add_argument('--region', required=True, help='AWS区域')
    parser.add_argument('--bucket', required=True, help='S3桶名称')
    parser.add_argument('--s3-key-prefix', default='', help='S3键前缀，所有上传文件的S3键将以此为前缀')
    parser.add_argument('--debug', action='store_true', help='启用调试模式，显示更详细的日志信息')
    
    args = parser.parse_args()

    # 获取AWS凭证（优先使用命令行参数，其次使用环境变量）
    access_key = args.access_key or os.environ.get('AWS_ACCESS_KEY_ID')
    secret_key = args.secret_key or os.environ.get('AWS_SECRET_ACCESS_KEY')

    if not access_key or not secret_key:
        print("错误: 必须提供AWS凭证")
        print("方式1: 使用命令行参数 --access-key 和 --secret-key")
        print("方式2: 设置环境变量 AWS_ACCESS_KEY_ID 和 AWS_SECRET_ACCESS_KEY")
        sys.exit(1)

    uploader = S3Uploader(
        access_key=access_key,
        secret_key=secret_key,
        region=args.region,
        bucket_name=args.bucket,
        s3_key_prefix=args.s3_key_prefix
    )
    
    results = uploader.process_folders_and_upload(
        args.base_dir,
        args.debug
    )
    
    print("\n上传结果摘要:")
    print(f"总计: {results['total']} 文件")
    print(f"成功: {results['success']} 文件")
    print(f"跳过: {results['skipped']} 文件 (已存在于S3)")
    print(f"失败: {results['failed']} 文件")
    
    print("\nBanner:")
    print(f"总计: {results['details']['banner']['total']} 文件")
    print(f"成功上传: {results['details']['banner']['success']} 文件")
    print(f"跳过上传: {results['details']['banner']['skipped']} 文件")
    print(f"失败: {results['details']['banner']['failed']} 文件")

    print("\nHaveFun:")
    print(f"总计: {results['details']['havefun']['total']} 文件")
    print(f"成功上传: {results['details']['havefun']['success']} 文件")
    print(f"跳过上传: {results['details']['havefun']['skipped']} 文件")
    print(f"失败: {results['details']['havefun']['failed']} 文件")