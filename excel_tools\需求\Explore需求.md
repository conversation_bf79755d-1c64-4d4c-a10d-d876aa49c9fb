我有一个excel文件 3month_data.xlsx 和 素材文件Explore。
1、我想写一个python工具类，实现将我的excel文件中，各sheet页的内容跟explore中对应的文件上传到aws s3桶。其中sheet页-【1 Product Rec】与 explore 的Banner文件夹中的内容对应，对应关系是【1 Product Rec】的commodity_model是Banner中文件的文件名；sheet页-【2 Have Fun】与 explore 的Have Fun文件夹中的内容对应，对应关系是【2 Have Fun】的content_no是Have Fun中文件的文件名。注意：sheet页跟explore中文件没对应上的不需要上传s3。
> 工具需要支持查找base-dir下子文件夹中的文件，且需要获取文件的全路径：file_path
> 访问s3通过aksk，需要代码配置为动态参数
> s3桶需要配置为动态参数
> 上传至s3的s3_key_prefix需要配置为动态参数，最终文件的 s3_key = s3_key_prefix + file_path 
> 区域需要配置为动态参数

2、写一个python工具，实现将excel文件中各个sheet页的内容整理成可在mysql数据库运行的insert sql语句。
> 【1 Product Rec】对应表 t_promotion_product，表字段与excel首行的字段一致，且除id、image_no和is_deleted为int外其余按字符串处理
> 【2 Have Fun】对应表 t_have_fun，表字段与excel首行的字段一致，且除id和is_deleted为int外其余按字符串处理
> 【3 Accessory Rec】对应表 t_recommend_accessory，表字段与excel首行的字段一致，且除id和is_deleted为int外其余按字符串处理
