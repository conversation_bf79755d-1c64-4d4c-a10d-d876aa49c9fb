1、我有一张这样的mysql表iot_platform.device_code，表字段及类型为：id bigint, device_id varchar(128), sn varchar(128), product_sn_code varchar(45), mes varchar(128), mo_code varchar(128), item_code varchar(128), production_date date, iccid varchar(30), status tinyint, is_deleted tinyint, create_by varchar(200), create_time timestamp, update_by varchar(200), update_time timestamp, mes_create_time bigint。

2、我想为这张表插入6000000条记录。需要整体插入三类设备：
> 一类设备插入5500000:id是19位数字递增；device_id随机生成如device00000001，且逐序递增；sn随机生成如NTS660000000001，且逐序递增；product_sn_code为固定值TS66，mes为固定值1，mo_code为固定值1，item_code为固定值1，production_date为固定值2025-04-18，iccid为固定值1，status为固定值1，is_deleted为固定值0，create_by为固定值10000，create_time为固定值now()，update_by为固定值10000，update_time为固定值now()，mes_create_time为固定值1756242424216

> 二类设备插入100000:id是19位数字递增；device_id随机生成如XHT120000000001，且逐序递增；sn随机生成如NHT120000000001，且逐序递增；product_sn_code为固定值HT12，mes为固定值1，mo_code为固定值1，item_code为固定值1，production_date为固定值2025-04-18，iccid为固定值1，status为固定值1，is_deleted为固定值0，create_by为固定值10000，create_time为固定值now()，update_by为固定值10000，update_time为固定值now()，mes_create_time为固定值1756242424216

> 三类设备插入400000:id是19位数字递增；device_id随机生成如XHT200000000001，且逐序递增；sn随机生成如NHT200000000001，且逐序递增；product_sn_code为固定值HT20，mes为固定值1，mo_code为固定值1，item_code为固定值1，production_date为固定值2025-04-18，iccid为固定值1，status为固定值1，is_deleted为固定值0，create_by为固定值10000，create_time为固定值now()，update_by为固定值10000，update_time为固定值now()，mes_create_time为固定值1756242424216

3、数据库连接信息：url、port、password、user需要做成动态参数