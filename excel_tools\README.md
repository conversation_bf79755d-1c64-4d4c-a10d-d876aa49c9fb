# Excel Tools 操作文档

## 📋 项目概述

Excel Tools 是一个用于处理 Excel 数据文件的工具集，主要包含三个核心功能：
1. **S3文件上传** - 将Banner和HaveFun文件夹中的文件批量上传到AWS S3
2. **SQL语句生成** - 将Excel数据转换为MySQL INSERT/UPDATE语句
3. **URL批量更新** - 批量更新Excel文件中的URL字段

---

## 🗂️ 文件结构

```
excel_tools/
├── s3_uploader_final.py      # S3文件上传工具
├── sql_generator_fixed.py    # SQL语句生成工具
├── update_urls.py            # URL批量更新工具
├── README.md                 # 本文档
├── 技术实现方案.md           # 技术实现详细方案
├── update_urls_使用说明.md   # URL更新工具详细说明
└── 需求/                     # 原始需求文档
    ├── Explore需求.md
    └── insert需求.md
```

---

## 🚀 工具详细说明

### 1. S3文件上传工具 (`s3_uploader_final.py`)

#### 功能描述
将Banner和HaveFun文件夹中的所有文件批量上传到AWS S3存储桶，支持自动跳过已存在的文件。

#### 主要特性
- ✅ 递归扫描Banner和HaveFun文件夹
- ✅ 自动跳过已存在于S3的文件
- ✅ 保持原有目录结构
- ✅ 支持调试模式和详细日志
- ✅ 统计上传结果（成功/跳过/失败）

#### 使用方法

**基本用法：**
```bash
python s3_uploader_final.py \
  --base-dir .. \
  --region us-east-1 \
  --bucket your-bucket-name \
  --s3-key-prefix explore/ \
  --access-key YOUR_ACCESS_KEY \
  --secret-key YOUR_SECRET_KEY
```

**使用环境变量：**
```bash
# 设置环境变量
export AWS_ACCESS_KEY_ID=YOUR_ACCESS_KEY
export AWS_SECRET_ACCESS_KEY=YOUR_SECRET_KEY

# 运行脚本
python s3_uploader_final.py \
  --base-dir .. \
  --region us-east-1 \
  --bucket your-bucket-name \
  --s3-key-prefix explore/ \
  --debug
```

#### 参数说明
| 参数 | 必需 | 说明 |
|------|------|------|
| `--base-dir` | ✅ | 基础目录路径（包含Banner和HaveFun文件夹） |
| `--region` | ✅ | AWS区域（如：us-east-1） |
| `--bucket` | ✅ | S3桶名称 |
| `--s3-key-prefix` | ❌ | S3键前缀（默认为空） |
| `--access-key` | ❌ | AWS访问密钥（可用环境变量） |
| `--secret-key` | ❌ | AWS秘密密钥（可用环境变量） |
| `--debug` | ❌ | 启用调试模式 |

#### 输出示例
```
上传结果摘要:
总计: 55 文件
成功: 0 文件
跳过: 55 文件 (已存在于S3)
失败: 0 文件

Banner:
总计: 15 文件
成功上传: 0 文件
跳过上传: 15 文件
失败: 0 文件

HaveFun:
总计: 40 文件
成功上传: 0 文件
跳过上传: 40 文件
失败: 0 文件
```

---

### 2. SQL语句生成工具 (`sql_generator_fixed.py`)

#### 功能描述
将Excel文件中的数据转换为MySQL INSERT或UPDATE语句，支持多个sheet页面的数据处理。

#### 主要特性
- ✅ 支持INSERT和UPDATE两种SQL类型
- ✅ 自动处理数据类型转换
- ✅ 特殊字段处理（时间、创建者等）
- ✅ 按表分文件输出SQL语句
- ✅ UPDATE语句按id字段进行更新

#### 表映射关系
| Excel Sheet | MySQL表名 | 特殊字段处理 |
|-------------|-----------|-------------|
| `1 Product Rec` | `t_promotion_product` | id, image_no, is_deleted为int |
| `2 Have Fun` | `t_have_fun` | id, is_deleted为int |
| `3 Accessory Rec` | `t_recommend_accessory` | id, is_deleted为int |

#### 使用方法

**生成INSERT语句：**
```bash
python sql_generator_fixed.py \
  --excel ../3month_data.xlsx \
  --output-dir sql_output \
  --sql-type insert
```

**生成UPDATE语句：**
```bash
python sql_generator_fixed.py \
  --excel ../3month_data.xlsx \
  --output-dir sql_output \
  --sql-type update
```

#### 参数说明
| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--excel` | ✅ | 无 | Excel文件路径 |
| `--output-dir` | ❌ | `sql_output` | SQL输出目录 |
| `--sql-type` | ❌ | `insert` | SQL类型：insert 或 update |

#### 特殊字段处理规则
- **时间字段** (`create_time`, `update_time`)：空值时使用 `now()`
- **创建者字段** (`create_by`, `update_by`)：空值时使用 `9999`
- **链接字段** (`link_in_text`)：空值时使用空字符串 `''`
- **数字字段**：自动转换为整数（无引号）
- **字符串字段**：自动转义单引号

#### 输出示例
```
处理结果摘要:
SQL类型: UPDATE
处理sheet数: 3
处理总行数: 59

1 Product Rec -> t_promotion_product:
  行数: 5
  SQL语句数: 5

生成的SQL文件:
  - sql_output/t_promotion_product_update.sql
  - sql_output/t_have_fun_update.sql
  - sql_output/t_recommend_accessory_update.sql
```

---

### 3. URL批量更新工具 (`update_urls.py`)

#### 功能描述
批量更新Excel文件中的URL字段，支持基于文件映射的动态URL生成。

#### 主要特性
- ✅ 支持【1 Product Rec】和【2 Have Fun】两个模块
- ✅ 自动文件映射和URL生成
- ✅ 支持备份原始Excel文件
- ✅ 详细的处理日志和统计信息

#### 使用方法

**基本用法：**
```bash
python update_urls.py --url https://your-cdn-domain.cloudfront.net/explore
```

**完整参数：**
```bash
python update_urls.py \
  --url https://your-cdn-domain.cloudfront.net/explore \
  --excel ../3month_data.xlsx \
  --havefun ../HaveFun \
  --banner ../Banner \
  --backup \
  --log update.log \
  --debug
```

#### 参数说明
| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--url` | ✅ | 无 | 基础URL（如CDN域名） |
| `--excel` | ❌ | `3month_data.xlsx` | Excel文件路径 |
| `--havefun` | ❌ | `HaveFun` | HaveFun目录路径 |
| `--banner` | ❌ | 自动推导 | Banner目录路径 |
| `--backup` | ❌ | False | 是否备份原始Excel文件 |
| `--log` | ❌ | 无 | 日志文件路径 |
| `--debug` | ❌ | False | 启用调试模式 |

#### URL更新规则
- **【2 Have Fun】模块**：
  - `avatar_url`: 固定为 `{base_url}/Banner/20250317/mask_group.png`
  - `iv_url`: 基于content_no映射为 `{base_url}/HaveFun/{子目录}/{content_no}.{扩展名}`

- **【1 Product Rec】模块**：
  - `image_url`: 基于commodity_model映射为 `{base_url}/Banner/{子目录}/{commodity_model}.{扩展名}`

---

## 📋 使用前准备

### 1. 环境要求
- Python 3.7+
- 必需的Python包：
  ```bash
  pip install pandas openpyxl boto3
  ```

### 2. 文件结构要求
```
项目根目录/
├── 3month_data.xlsx          # Excel数据文件
├── Banner/                   # 产品推广素材
│   ├── 20250317/
│   ├── 20250605/
│   └── ...
├── HaveFun/                  # 娱乐内容素材
│   ├── 20250317/
│   ├── 20250415/
│   └── ...
└── excel_tools/
    ├── s3_uploader_final.py
    ├── sql_generator_fixed.py
    └── update_urls.py
```

### 3. AWS配置（仅S3上传工具需要）
- AWS访问密钥ID (Access Key ID)
- AWS秘密访问密钥 (Secret Access Key)
- S3桶名称和权限
- 目标AWS区域

---

## 🔧 常见问题

### Q1: S3上传失败怎么办？
**A**: 检查以下项目：
- AWS凭证是否正确
- S3桶是否存在且有写入权限
- 网络连接是否正常
- 文件路径是否正确

### Q2: SQL生成的UPDATE语句需要id字段吗？
**A**: 是的，UPDATE模式要求Excel数据中必须包含id列，脚本会按id字段进行更新。

### Q3: URL更新工具找不到对应文件怎么办？
**A**: 检查文件名映射规则：
- HaveFun: 文件名必须是纯数字
- Banner: 文件名必须与commodity_model完全匹配

### Q4: 如何处理大量文件的上传？
**A**: 建议：
- 使用 `--debug` 参数监控进度
- 分批处理大量文件
- 确保网络连接稳定

---

## � 实际使用示例

### 完整工作流程示例

假设您有以下文件结构：
```
/project/
├── 3month_data.xlsx
├── Banner/
│   ├── 20250317/
│   │   ├── HPW3200.png
│   │   └── LM2135SP.png
│   └── 20250605/
│       └── ST1623T.png
├── HaveFun/
│   ├── 20250317/
│   │   ├── 1.jpg
│   │   ├── 2.mp4
│   │   └── 15.mp4
│   └── 20250415/
│       └── 31.jpg
└── excel_tools/
```

#### 步骤1: 上传文件到S3
```bash
cd excel_tools
python s3_uploader_final.py \
  --base-dir .. \
  --region us-east-1 \
  --bucket my-content-bucket \
  --s3-key-prefix explore/ \
  --debug
```

#### 步骤2: 更新Excel中的URL
```bash
python update_urls.py \
  --url https://d1234567890.cloudfront.net/explore \
  --excel ../3month_data.xlsx \
  --backup \
  --debug
```

#### 步骤3: 生成SQL语句
```bash
# 生成INSERT语句
python sql_generator_fixed.py \
  --excel ../3month_data.xlsx \
  --output-dir sql_output \
  --sql-type insert

# 生成UPDATE语句
python sql_generator_fixed.py \
  --excel ../3month_data.xlsx \
  --output-dir sql_output \
  --sql-type update
```

### 批处理脚本示例

创建 `batch_process.bat` (Windows) 或 `batch_process.sh` (Linux/Mac)：

**Windows批处理：**
```batch
@echo off
cd excel_tools

echo 开始上传文件到S3...
python s3_uploader_final.py --base-dir .. --region us-east-1 --bucket my-bucket --s3-key-prefix explore/ --debug

echo 更新Excel中的URL...
python update_urls.py --url https://cdn.example.com/explore --excel ../3month_data.xlsx --backup

echo 生成SQL语句...
python sql_generator_fixed.py --excel ../3month_data.xlsx --output-dir sql_output --sql-type insert
python sql_generator_fixed.py --excel ../3month_data.xlsx --output-dir sql_output --sql-type update

echo 处理完成！
pause
```

**Linux/Mac脚本：**
```bash
#!/bin/bash
cd excel_tools

echo "开始上传文件到S3..."
python3 s3_uploader_final.py --base-dir .. --region us-east-1 --bucket my-bucket --s3-key-prefix explore/ --debug

echo "更新Excel中的URL..."
python3 update_urls.py --url https://cdn.example.com/explore --excel ../3month_data.xlsx --backup

echo "生成SQL语句..."
python3 sql_generator_fixed.py --excel ../3month_data.xlsx --output-dir sql_output --sql-type insert
python3 sql_generator_fixed.py --excel ../3month_data.xlsx --output-dir sql_output --sql-type update

echo "处理完成！"
```

---

## 🔍 输出文件说明

### S3上传工具输出
- **控制台输出**：实时显示上传进度和结果统计
- **无文件输出**：所有信息通过控制台显示

### SQL生成工具输出
生成的SQL文件位于指定的输出目录中：
```
sql_output/
├── t_promotion_product_insert.sql    # Product Rec INSERT语句
├── t_promotion_product_update.sql    # Product Rec UPDATE语句
├── t_have_fun_insert.sql            # Have Fun INSERT语句
├── t_have_fun_update.sql            # Have Fun UPDATE语句
├── t_recommend_accessory_insert.sql  # Accessory Rec INSERT语句
└── t_recommend_accessory_update.sql  # Accessory Rec UPDATE语句
```

### URL更新工具输出
- **更新的Excel文件**：原文件直接更新，或创建新文件（如果原文件被锁定）
- **备份文件**：如果启用 `--backup`，会创建 `.backup` 后缀的备份文件
- **日志文件**：如果指定 `--log`，会生成详细的处理日志

---

## �📞 技术支持

如需技术支持或功能扩展，请参考：
- `技术实现方案.md` - 详细技术实现
- `update_urls_使用说明.md` - URL更新工具详细说明
- 各工具的 `--help` 参数查看完整选项

### 获取帮助
```bash
# 查看各工具的完整参数说明
python s3_uploader_final.py --help
python sql_generator_fixed.py --help
python update_urls.py --help
```

---

*最后更新：2025-06-05*
