# Excel 文件处理与 S3 上传技术实现方案

基于您提供的需求文档，我设计了一个完整的技术实现方案，包含两个主要组件：S3文件上传工具和SQL语句生成工具。

## 一、总体架构

整个方案包括两个独立的工具类：
1. `S3Uploader` - 负责将Excel数据对应的文件上传到AWS S3
2. `SQLGenerator` - 负责将Excel数据转换为MySQL INSERT语句

## 二、S3 上传工具详细设计

### 1. 核心组件设计

```python
class S3Uploader:
    def __init__(self, access_key, secret_key, region, bucket_name, s3_key_prefix=""):
        # AWS认证与配置初始化
        
    def process_excel_and_upload(self, excel_path, base_dir, debug=False):
        # 主处理函数
        
    def _find_matching_file(self, directory, file_name, debug=False):
        # 文件查找与匹配算法
        
    def upload_file(self, file_path, s3_key):
        # S3上传实现
```

### 2. 文件匹配算法

文件匹配将采用多级优先级匹配策略：
1. 完全匹配（如 "HPW3200.png" 匹配 "HPW3200.png"）
2. 不带扩展名匹配（如 "HPW3200" 匹配 "HPW3200.png"）
3. 忽略大小写匹配
4. 部分匹配（文件名包含字符串）

每种匹配类型都有相应的优先级，确保在多个匹配中选择最佳结果。

### 3. 对应关系处理

- 【1 Product Rec】sheet 页中的 commodity_model 字段与 Banner 文件夹文件匹配
- 【2 Have Fun】sheet 页中的 content_no 字段与 Have Fun 文件夹文件匹配

### 4. 异常处理机制

- 对文件不存在、路径错误等情况进行错误处理
- 提供详细日志记录与调试选项
- 支持重复上传检测，避免重复处理相同文件

### 5. 可配置参数

- AWS 访问凭证（access_key, secret_key）
- AWS 区域（region）
- S3桶名称（bucket_name）
- S3文件前缀（s3_key_prefix）
- Excel文件路径
- 素材基础路径（base_dir）

## 三、SQL 生成工具详细设计

### 1. 核心组件设计

```python
class SqlGenerator:
    def __init__(self):
        # 表名映射初始化
        
    def process_excel_to_sql(self, excel_path, output_dir=None):
        # 主处理函数
        
    def generate_insert_sql(self, df, table_name):
        # 生成INSERT SQL语句
        
    def _format_value(self, value, column_name, table_name):
        # 格式化SQL值
```

### 2. 表名与字段映射

- 【1 Product Rec】→ t_promotion_product
- 【2 Have Fun】→ t_have_fun
- 【3 Accessory Rec】→ t_recommend_accessory

### 3. 数据类型处理

根据需求设置相应字段类型：
- id, image_no, is_deleted 作为 int 处理
- 其他字段作为字符串处理
- 空值处理为 NULL（link_in_text 列特殊处理为空字符串）

### 4. SQL 文件生成

- 支持按表名分文件输出
- 为每个文件添加注释与统计信息
- 自动处理文件路径与创建目录

## 四、技术实现关键点

### 1. AWS SDK 集成

使用 boto3 库与 AWS S3 进行交互：
```python
import boto3

s3_client = boto3.client(
    's3',
    aws_access_key_id=access_key,
    aws_secret_access_key=secret_key,
    region_name=region
)

# 上传文件
s3_client.upload_file(local_path, bucket_name, s3_key)
```

### 2. Excel 文件处理

使用 pandas 库处理 Excel 文件：
```python
import pandas as pd

# 读取Excel文件
excel_file = pd.ExcelFile(excel_path)
df = pd.read_excel(excel_file, sheet_name=sheet_name, dtype=str)
```

### 3. 文件递归搜索

使用 os.walk 递归搜索文件：
```python
for root, _, files in os.walk(directory):
    for file in files:
        # 文件处理逻辑
```

### 4. 文件路径处理

使用 os.path 处理文件路径：
```python
# 获取相对路径
relative_path = os.path.relpath(file_path, base_dir)

# 格式化路径分隔符
normalized_path = relative_path.replace('\\', '/')
```

## 五、命令行接口设计

两个工具均支持命令行调用：

```
# S3上传工具
python s3_uploader.py --excel <Excel文件> --base-dir <素材基础路径> --access-key <访问密钥> --secret-key <秘密密钥> --region <区域> --bucket <S3桶> [--s3-key-prefix <前缀>] [--debug]

# SQL生成工具
python sql_generator.py --excel <Excel文件> [--output-dir <SQL输出目录>]
```

## 六、扩展性与维护性考虑

1. **模块化设计**：各功能模块独立，便于维护和扩展
2. **配置外部化**：关键参数通过命令行或配置文件传入
3. **详细日志**：提供不同级别日志记录，便于排错
4. **异常处理**：完善的错误处理机制
