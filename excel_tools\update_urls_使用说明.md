# update_urls.py 使用说明

## 📋 功能概述

`update_urls.py` 是一个用于批量更新Excel文件中URL字段的工具，支持同时处理两个业务模块：

### 🎯 **支持的业务模块**
1. **【2 Have Fun】娱乐内容模块**
   - 更新 `avatar_url` 字段（固定URL）
   - 更新 `iv_url` 字段（基于content_no动态映射）

2. **【1 Product Rec】产品推荐模块** ⭐ **新增功能**
   - 更新 `image_url` 字段（基于commodity_model动态映射）

---

## 🚀 快速开始

### **基本使用**
```bash
python update_urls.py --url https://your-cdn-domain.cloudfront.net/explore
```

### **完整参数使用**
```bash
python update_urls.py \
  --url https://your-cdn-domain.cloudfront.net/explore \
  --excel ../3month_data.xlsx \
  --havefun ../HaveFun \
  --banner ../Banner \
  --backup \
  --log update.log \
  --debug
```

---

## 📋 命令行参数

| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--url` | ✅ | 无 | 基础URL（如CDN域名） |
| `--excel` | ❌ | `3month_data.xlsx` | Excel文件路径 |
| `--havefun` | ❌ | `HaveFun` | HaveFun目录路径 |
| `--banner` | ❌ | 自动推导 | Banner目录路径 |
| `--backup` | ❌ | False | 是否备份原始Excel文件 |
| `--log` | ❌ | 无 | 日志文件路径 |
| `--debug` | ❌ | False | 启用调试模式 |

---

## 🔧 URL更新逻辑

### **【2 Have Fun】模块**
```
avatar_url: {base_url}/Banner/20250317/mask_group.png (固定)
iv_url: {base_url}/HaveFun/{子目录}/{content_no}.{扩展名}
```

**示例**：
```
基础URL: https://cdn.example.com/explore
content_no: 15

生成结果:
avatar_url: https://cdn.example.com/explore/Banner/20250317/mask_group.png
iv_url: https://cdn.example.com/explore/HaveFun/20250317/15.mp4
```

### **【1 Product Rec】模块** ⭐ **新增**
```
image_url: {base_url}/Banner/{子目录}/{commodity_model}.{扩展名}
```

**示例**：
```
基础URL: https://cdn.example.com/explore
commodity_model: LM2135SP

生成结果:
image_url: https://cdn.example.com/explore/Banner/20250601/LM2135SP.png
```

---

## 📁 文件结构要求

### **目录结构**
```
项目根目录/
├── 3month_data.xlsx          # Excel数据文件
├── HaveFun/                  # 娱乐内容素材
│   ├── 20250317/
│   │   ├── 1.jpg
│   │   ├── 2.jpg
│   │   └── ...
│   └── 20250415/
│       ├── 31.jpg
│       └── ...
├── Banner/                   # 产品推广素材
│   ├── 20250317/
│   │   ├── LM2135SP.png
│   │   ├── ST1623T.png
│   │   └── ...
│   ├── 20250513/
│   └── 20250601/
└── excel_tools/
    └── update_urls.py
```

### **Excel文件要求**
- 必须包含 `【2 Have Fun】` 和/或 `【1 Product Rec】` sheet
- **【2 Have Fun】** 必须包含 `content_no`, `avatar_url`, `iv_url` 列
- **【1 Product Rec】** 必须包含 `commodity_model`, `image_url` 列

---

## 🎯 文件映射策略

### **HaveFun文件映射**
- **匹配规则**: 文件名（不含扩展名）必须是数字
- **支持格式**: 所有文件类型
- **映射逻辑**: `content_no` → 文件路径

```python
# 示例映射
1 → HaveFun/20250317/1.jpg
15 → HaveFun/20250317/15.mp4
31 → HaveFun/20250415/31.jpg
```

### **Banner文件映射** ⭐ **新增**
- **匹配规则**: 文件名（不含扩展名）与commodity_model完全匹配
- **支持格式**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`
- **映射逻辑**: `commodity_model` → 文件路径

```python
# 示例映射
LM2135SP → Banner/20250601/LM2135SP.png
ST1623T → Banner/20250317/ST1623T.png
MB1000 → Banner/20250317/MB1000.png
```

### **重复文件处理**
当同一个commodity_model在多个目录中存在时：
- 脚本会选择最后遍历到的文件
- 建议保持文件名的唯一性或使用最新版本

---

## 📊 使用示例

### **示例1: 基本使用**
```bash
# 更新所有URL，使用默认路径
python excel_tools/update_urls.py --url https://d123456.cloudfront.net/explore
```

### **示例2: 指定路径和备份**
```bash
# 指定所有路径，启用备份和日志
python excel_tools/update_urls.py \
  --url https://d123456.cloudfront.net/explore \
  --excel data/marketing_data.xlsx \
  --havefun assets/HaveFun \
  --banner assets/Banner \
  --backup \
  --log logs/url_update.log
```

### **示例3: 调试模式**
```bash
# 启用调试模式，查看详细处理过程
python excel_tools/update_urls.py \
  --url https://d123456.cloudfront.net/explore \
  --debug
```

---

## 📈 处理结果示例

### **成功执行输出**
```
2025-06-05 10:12:29,748 - INFO - === 处理统计 ===
2025-06-05 10:12:29,748 - INFO - 【2 Have Fun】: 更新40条，未找到0条
2025-06-05 10:12:29,748 - INFO - 【1 Product Rec】: 更新5条，未找到0条
2025-06-05 10:12:29,748 - INFO - 总计: 更新45条记录，0条记录未找到对应文件
```

### **更新前后对比**
```
更新前:
【1 Product Rec】image_url: 空值或旧URL
【2 Have Fun】iv_url: 空值或旧URL

更新后:
【1 Product Rec】image_url: https://cdn.example.com/explore/Banner/20250601/LM2135SP.png
【2 Have Fun】iv_url: https://cdn.example.com/explore/HaveFun/20250317/15.mp4
```

---

## ❗ 常见问题

### **Q1: 提示"目录不存在"**
**A**: 检查HaveFun和Banner目录路径是否正确
```bash
# 使用绝对路径或正确的相对路径
--havefun /full/path/to/HaveFun
--banner /full/path/to/Banner
```

### **Q2: 部分文件未找到对应映射**
**A**: 检查文件名是否符合映射规则
- HaveFun: 文件名必须是纯数字
- Banner: 文件名必须与commodity_model完全匹配

### **Q3: Excel文件被锁定无法写入**
**A**: 脚本会自动创建新文件
```
无法直接更新Excel文件，创建新文件: 3month_data_updated.xlsx
```

### **Q4: 重复文件如何处理**
**A**: 脚本会选择最后遍历到的文件，建议：
- 保持文件名唯一性
- 删除旧版本文件
- 使用最新的文件版本

---

## 🔍 验证结果

### **手动验证**
```python
import pandas as pd

# 检查更新结果
excel = pd.ExcelFile('3month_data.xlsx')

# 验证【1 Product Rec】
df_product = pd.read_excel(excel, sheet_name='1 Product Rec')
print(df_product[['commodity_model', 'image_url']])

# 验证【2 Have Fun】
df_havefun = pd.read_excel(excel, sheet_name='2 Have Fun')
print(df_havefun[['content_no', 'iv_url']].head())
```

### **使用测试脚本**
```bash
# 运行完整测试
python excel_tools/test_update_urls.py
```

---

## 💡 最佳实践

1. **执行前备份**: 始终使用 `--backup` 参数
2. **使用日志**: 重要操作时启用 `--log` 参数
3. **调试模式**: 首次使用时启用 `--debug` 模式
4. **文件整理**: 保持目录结构清晰，避免重复文件名
5. **验证结果**: 执行后验证URL的正确性

---

## 🔄 版本更新

### **v2.0 新增功能**
- ✅ 支持【1 Product Rec】sheet的image_url更新
- ✅ 智能Banner文件映射
- ✅ 重复文件处理机制
- ✅ 详细的处理统计信息
- ✅ 向后兼容原有功能

### **向后兼容性**
- 保持原有【2 Have Fun】处理逻辑不变
- 保持原有命令行参数接口
- 新增功能不影响现有工作流程

---

*最后更新: 2024年12月 | 版本: v2.0*
