-- 表 t_recommend_accessory 的INSERT语句 (来自 3 Accessory Rec)
-- 共 14 行数据
-- 生成时间: 2025-06-30 14:15:07

INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000001, 'Riding Mowers', '4927', 'TR4200', 'TR01', 'ABK4200T', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000002, 'Riding Mowers', '4913', 'ZT4200L', 'ZT01', 'AMP4200', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000003, 'Riding Mowers', '4913.1', 'ZT4200S', 'ZT03', 'AMP4200', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000004, 'Riding Mowers', '4919', 'ZT5200L', 'ZT02', 'ABK5200', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000005, 'Blowers', '82008', 'LB8800', 'LB20', 'AST2000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000006, 'Blowers', '4708.1', 'LB6500', 'LB10', 'AST2000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000007, 'Blowers', '4708.2', 'LB6150', 'LB11', 'AST2000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000008, 'Blowers', '4716', 'LB7650', 'LB12', 'AST2000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000009, 'Blowers', '4708.1', 'LB6700', 'LB14', 'AGC1000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000010, 'Blowers', '4708', 'LB5800', 'LB07', 'AGC1000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000011, 'Blowers', '4704', 'LB5300', 'LB03', 'AGC1000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000012, 'Blowers', '4704.1', 'LB5750', 'LB06', 'AGC1000', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000013, 'Lifestyle', '87006', 'HPW2100', 'HP03', 'ANK2100', 9999, now(), 9999, now(), 0);
INSERT INTO `iot_app`.`t_recommend_accessory` (`id`, `category`, `model`, `commodity_model`, `product_sn_code`, `recommended_accessory`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES (3000014, 'Lifestyle', '87004', 'HPW3200', 'HP01', 'ANK3200', 9999, now(), 9999, now(), 0);