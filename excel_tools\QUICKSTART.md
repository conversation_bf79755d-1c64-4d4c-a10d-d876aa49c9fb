# Excel Tools 快速入门指南

## 🚀 5分钟快速上手

### 前置条件
1. **Python环境**：Python 3.7+
2. **安装依赖**：
   ```bash
   pip install pandas openpyxl boto3
   ```
3. **文件准备**：确保有 `3month_data.xlsx` 和 `Banner/`、`HaveFun/` 文件夹

---

## 📋 三个核心工具

| 工具 | 功能 | 输入 | 输出 |
|------|------|------|------|
| `s3_uploader_final.py` | 上传文件到S3 | 文件夹 | S3对象 |
| `sql_generator_fixed.py` | 生成SQL语句 | Excel | SQL文件 |
| `update_urls.py` | 更新URL字段 | Excel+文件夹 | 更新的Excel |

---

## 🎯 常用命令速查

### 1. 上传文件到S3
```bash
# 基本用法
python s3_uploader_final.py \
  --base-dir .. \
  --region us-east-1 \
  --bucket your-bucket \
  --s3-key-prefix explore/

# 使用环境变量（推荐）
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
python s3_uploader_final.py --base-dir .. --region us-east-1 --bucket your-bucket --s3-key-prefix explore/ --debug
```

### 2. 生成SQL语句
```bash
# 生成INSERT语句
python sql_generator_fixed.py --excel ../3month_data.xlsx --sql-type insert

# 生成UPDATE语句
python sql_generator_fixed.py --excel ../3month_data.xlsx --sql-type update
```

### 3. 更新Excel URL
```bash
# 基本用法
python update_urls.py --url https://your-cdn.cloudfront.net/explore

# 完整参数
python update_urls.py --url https://your-cdn.cloudfront.net/explore --backup --debug
```

---

## 📁 标准文件结构

```
项目目录/
├── 3month_data.xlsx          # 📊 Excel数据文件
├── Banner/                   # 🖼️ 产品图片
│   ├── 20250317/
│   │   ├── HPW3200.png
│   │   └── LM2135SP.png
│   └── 20250605/
│       └── ST1623T.png
├── HaveFun/                  # 🎬 娱乐内容
│   ├── 20250317/
│   │   ├── 1.jpg
│   │   ├── 2.mp4
│   │   └── 15.mp4
│   └── 20250415/
│       └── 31.jpg
└── excel_tools/              # 🛠️ 工具脚本
    ├── s3_uploader_final.py
    ├── sql_generator_fixed.py
    └── update_urls.py
```

---

## ⚡ 一键批处理

### Windows用户
创建 `run_all.bat`：
```batch
@echo off
cd excel_tools

REM 设置AWS凭证
set AWS_ACCESS_KEY_ID=your_access_key
set AWS_SECRET_ACCESS_KEY=your_secret_key

REM 1. 上传文件到S3
echo [1/3] 上传文件到S3...
python s3_uploader_final.py --base-dir .. --region us-east-1 --bucket your-bucket --s3-key-prefix explore/

REM 2. 更新Excel URL
echo [2/3] 更新Excel URL...
python update_urls.py --url https://your-cdn.cloudfront.net/explore --backup

REM 3. 生成SQL语句
echo [3/3] 生成SQL语句...
python sql_generator_fixed.py --excel ../3month_data.xlsx --sql-type insert
python sql_generator_fixed.py --excel ../3month_data.xlsx --sql-type update

echo 完成！
pause
```

### Linux/Mac用户
创建 `run_all.sh`：
```bash
#!/bin/bash
cd excel_tools

# 设置AWS凭证
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key

# 1. 上传文件到S3
echo "[1/3] 上传文件到S3..."
python3 s3_uploader_final.py --base-dir .. --region us-east-1 --bucket your-bucket --s3-key-prefix explore/

# 2. 更新Excel URL
echo "[2/3] 更新Excel URL..."
python3 update_urls.py --url https://your-cdn.cloudfront.net/explore --backup

# 3. 生成SQL语句
echo "[3/3] 生成SQL语句..."
python3 sql_generator_fixed.py --excel ../3month_data.xlsx --sql-type insert
python3 sql_generator_fixed.py --excel ../3month_data.xlsx --sql-type update

echo "完成！"
```

---

## 🔧 常见问题快速解决

### ❌ 问题：AWS凭证错误
```
错误: 必须提供AWS凭证
```
**解决**：
```bash
# 方法1: 设置环境变量
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret

# 方法2: 命令行参数
python s3_uploader_final.py --access-key your_key --secret-key your_secret ...
```

### ❌ 问题：找不到Excel文件
```
FileNotFoundError: [Errno 2] No such file or directory: '3month_data.xlsx'
```
**解决**：
```bash
# 检查文件路径
ls -la ../3month_data.xlsx

# 或指定完整路径
python sql_generator_fixed.py --excel /full/path/to/3month_data.xlsx
```

### ❌ 问题：S3桶权限不足
```
AccessDenied: Access Denied
```
**解决**：
- 检查AWS凭证是否有S3写入权限
- 确认S3桶名称正确
- 验证区域设置正确

### ❌ 问题：UPDATE语句缺少id列
```
ValueError: 表 t_promotion_product 的数据中缺少 'id' 列
```
**解决**：
- 确保Excel中包含id列
- 或使用INSERT模式：`--sql-type insert`

---

## 📊 输出结果说明

### S3上传结果
```
上传结果摘要:
总计: 55 文件
成功: 10 文件      # 新上传的文件
跳过: 45 文件      # 已存在的文件
失败: 0 文件       # 上传失败的文件
```

### SQL生成结果
```
sql_output/
├── t_promotion_product_insert.sql    # 产品推荐INSERT
├── t_have_fun_update.sql            # 娱乐内容UPDATE
└── ...
```

### URL更新结果
```
处理完成！
【1 Product Rec】: 5个URL已更新
【2 Have Fun】: 40个URL已更新
备份文件: 3month_data.xlsx.backup
```

---

## 🎯 下一步

1. **查看详细文档**：`README.md`
2. **了解技术实现**：`技术实现方案.md`
3. **URL工具详细说明**：`update_urls_使用说明.md`
4. **获取帮助**：
   ```bash
   python script_name.py --help
   ```

---

## 📞 需要帮助？

- 🐛 **遇到错误**：检查上面的常见问题
- 📖 **功能疑问**：查看 `README.md`
- ⚙️ **技术细节**：查看 `技术实现方案.md`
- 🔧 **参数说明**：使用 `--help` 参数

---

*快速入门指南 - 最后更新：2025-06-05*
