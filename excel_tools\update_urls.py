#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新Excel文件中的URL

此脚本用于更新Excel中的URL列：
- 【2 Have Fun】sheet: 更新iv_url、avatar_url和cover_page_url列
- 【1 Product Rec】sheet: 更新image_url列
"""

import os
import sys
import time
import argparse
import pandas as pd
import logging
from datetime import datetime


def setup_logging(log_file=None, level=logging.INFO):
    """配置日志"""
    log_format = "%(asctime)s - %(levelname)s - %(message)s"
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    logging.basicConfig(level=level, format=log_format, handlers=handlers)


def validate_directory(directory_path, directory_name="目录"):
    """检查指定目录下的文件"""
    if not os.path.exists(directory_path):
        logging.error(f"错误：{directory_name} {directory_path} 不存在")
        return False

    subdirs = [d for d in os.listdir(directory_path) if os.path.isdir(os.path.join(directory_path, d))]
    files = [f for f in os.listdir(directory_path) if os.path.isfile(os.path.join(directory_path, f))]

    if not subdirs and not files:
        logging.error(f"错误：{directory_name} {directory_path} 下没有文件或子目录")
        return False

    if subdirs:
        logging.info(f"{directory_name}发现 {len(subdirs)} 个子目录: {', '.join(subdirs)}")
        for subdir in subdirs:
            full_dir_path = os.path.join(directory_path, subdir)
            subdir_files = [f for f in os.listdir(full_dir_path) if os.path.isfile(os.path.join(full_dir_path, f))]
            if not subdir_files:
                logging.warning(f"警告：{directory_name}子目录 {subdir} 是空的")
            else:
                logging.info(f"{directory_name}子目录 {subdir} 中包含 {len(subdir_files)} 个文件")

    if files:
        logging.info(f"{directory_name}根目录包含 {len(files)} 个文件")

    return True


def normalize_path(path):
    """标准化路径格式"""
    return path.replace('\\', '/')


def create_file_mapping(directory_path, base_path, mapping_name, file_filter=None):
    """通用文件映射创建函数"""
    file_mapping = {}
    skipped_files = []

    if not os.path.exists(directory_path):
        logging.warning(f"{mapping_name}目录不存在: {directory_path}")
        return file_mapping

    # 遍历目录
    for root, _, files in os.walk(directory_path):
        for file in files:
            # 应用文件过滤器
            if file_filter and not file_filter(file):
                skipped_files.append(file)
                continue

            try:
                # 提取数字文件名作为键
                key = int(os.path.splitext(file)[0])
                relative_path = os.path.relpath(os.path.join(root, file), base_path)
                relative_path = normalize_path(relative_path)
                file_mapping[key] = relative_path
                logging.debug(f"{mapping_name}映射 {key} -> {relative_path}")
            except ValueError:
                skipped_files.append(file)
                continue

    if skipped_files:
        logging.warning(f"{mapping_name}警告：跳过了 {len(skipped_files)} 个文件: {', '.join(skipped_files[:5])}" +
                      (f" 等..." if len(skipped_files) > 5 else ""))

    logging.info(f"{mapping_name}找到 {len(file_mapping)} 个有效文件映射")
    return file_mapping


def get_havefun_file_mapping(have_fun_path):
    """创建content_no到HaveFun文件路径的映射"""
    return create_file_mapping(have_fun_path, os.path.dirname(have_fun_path), "HaveFun")


def get_cover_page_url_mapping(have_fun_path):
    """创建content_no到video_cover_page_url文件路径的映射"""
    cover_page_url_path = os.path.join(have_fun_path, 'video_cover_page_url')
    return create_file_mapping(cover_page_url_path, os.path.dirname(have_fun_path), "CoverPage")


def get_banner_file_mapping(banner_path):
    """创建commodity_model到Banner文件路径的映射，多个同名文件时选择最新的"""
    file_candidates = {}  # {commodity_model: [(mtime, relative_path), ...]}
    skipped_files = []
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

    if not os.path.exists(banner_path):
        logging.warning(f"Banner目录不存在: {banner_path}")
        return {}

    # 遍历Banner/下的所有目录
    for root, _, files in os.walk(banner_path):
        for file in files:
            file_lower = file.lower()
            file_ext = os.path.splitext(file_lower)[1]

            # 只处理图片文件
            if file_ext in image_extensions:
                commodity_model = os.path.splitext(file)[0]
                full_path = os.path.join(root, file)
                relative_path = os.path.relpath(full_path, os.path.dirname(banner_path))
                relative_path = normalize_path(relative_path)
                
                # 获取文件修改时间
                mtime = os.path.getmtime(full_path)
                
                if commodity_model not in file_candidates:
                    file_candidates[commodity_model] = []
                file_candidates[commodity_model].append((mtime, relative_path))
                
                logging.debug(f"Banner候选 {commodity_model} -> {relative_path} (mtime: {mtime})")
            else:
                skipped_files.append(file)

    # 为每个commodity_model选择最新的文件
    file_mapping = {}
    for commodity_model, candidates in file_candidates.items():
        # 按修改时间排序，选择最新的
        latest_file = max(candidates, key=lambda x: x[0])
        file_mapping[commodity_model] = latest_file[1]
        
        if len(candidates) > 1:
            logging.info(f"Banner {commodity_model} 有 {len(candidates)} 个文件，选择最新的: {latest_file[1]}")
        
        logging.debug(f"Banner映射 {commodity_model} -> {latest_file[1]}")

    if skipped_files:
        logging.warning(f"Banner警告：跳过了 {len(skipped_files)} 个非图片文件: {', '.join(skipped_files[:5])}" +
                      (f" 等..." if len(skipped_files) > 5 else ""))

    logging.info(f"Banner找到 {len(file_mapping)} 个有效文件映射")
    return file_mapping


def process_havefun_sheet(df, base_url, havefun_mapping, cover_page_url_mapping):
    """处理【2 Have Fun】工作表"""
    stats = {'updated': 0, 'not_found': 0}
    
    # 更新avatar_url列
    avatar_path = "/Banner/20250317/mask_group.png"
    df['avatar_url'] = f"{base_url}{avatar_path}"
    
    # 更新iv_url和cover_page_url列
    for index, row in df.iterrows():
        content_no = row['content_no']
        
        # 更新iv_url
        if content_no in havefun_mapping:
            file_path = havefun_mapping[content_no]
            df.at[index, 'iv_url'] = f"{base_url}/{file_path}"
            stats['updated'] += 1
        else:
            stats['not_found'] += 1
            logging.warning(f"警告：未找到content_no为{content_no}的对应文件")
        
        # 更新cover_page_url
        if content_no in cover_page_url_mapping:
            cover_path = cover_page_url_mapping[content_no]
            df.at[index, 'cover_page_url'] = f"{base_url}/{cover_path}"
    
    # 清理列
    if 'original_iv_url' in df.columns:
        df = df.drop(columns=['original_iv_url'])
    
    cover_page_url_updated = len([c for c in cover_page_url_mapping if c in df['content_no'].values])
    logging.info(f"【2 Have Fun】处理完成：更新iv_url {stats['updated']}条，更新cover_page_url {cover_page_url_updated}条，未找到{stats['not_found']}条")
    
    return df, stats


def process_product_sheet(df, base_url, banner_mapping):
    """处理【1 Product Rec】工作表"""
    stats = {'updated': 0, 'not_found': 0}
    
    # 更新image_url列
    for index, row in df.iterrows():
        commodity_model = str(row['commodity_model']) if pd.notna(row['commodity_model']) else ''
        if commodity_model and commodity_model in banner_mapping:
            file_path = banner_mapping[commodity_model]
            df.at[index, 'image_url'] = f"{base_url}/{file_path}"
            stats['updated'] += 1
        else:
            stats['not_found'] += 1
            if commodity_model:
                logging.warning(f"警告：未找到commodity_model为{commodity_model}的对应文件")
            else:
                logging.warning(f"警告：第{index+1}行的commodity_model为空")
    
    logging.info(f"【1 Product Rec】处理完成：更新{stats['updated']}条，未找到{stats['not_found']}条")
    return df, stats


def save_excel_file(excel_path, updated_sheets):
    """保存Excel文件"""
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            for sheet_name, df in updated_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                logging.debug(f"已写入sheet: {sheet_name}")
    except Exception as e:
        # 创建新文件作为备选方案
        new_excel_path = excel_path.replace('.xlsx', '_updated.xlsx')
        logging.warning(f"无法直接更新Excel文件，创建新文件: {new_excel_path}")
        
        with pd.ExcelFile(excel_path) as xls_read:
            with pd.ExcelWriter(new_excel_path, engine='openpyxl') as writer:
                for sheet_name in xls_read.sheet_names:
                    if sheet_name in updated_sheets:
                        updated_sheets[sheet_name].to_excel(writer, sheet_name=sheet_name, index=False)
                        logging.debug(f"已写入更新后的sheet: {sheet_name}")
                    else:
                        sheet_df = pd.read_excel(xls_read, sheet_name=sheet_name)
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        logging.debug(f"已复制原始sheet: {sheet_name}")


def update_excel_urls(excel_path, base_url, have_fun_path, banner_path=None):
    """更新Excel文件中的URL列"""
    logging.info(f"正在更新Excel文件: {excel_path}")
    logging.info(f"基础URL: {base_url}")
    logging.info(f"HaveFun路径: {have_fun_path}")
    
    if banner_path is None:
        banner_path = os.path.join(os.path.dirname(have_fun_path), 'Banner')
    logging.info(f"Banner路径: {banner_path}")
    
    start_time = time.time()
    base_url = base_url.rstrip('/')
    
    # 验证目录
    havefun_valid = validate_directory(have_fun_path, "HaveFun目录")
    banner_valid = validate_directory(banner_path, "Banner目录")
    
    if not havefun_valid and not banner_valid:
        logging.error("HaveFun和Banner目录都验证失败，退出操作。")
        return
    
    # 获取文件映射
    mappings = {}
    if havefun_valid:
        mappings['havefun'] = get_havefun_file_mapping(have_fun_path)
        mappings['cover_page_url'] = get_cover_page_url_mapping(have_fun_path)
        if not mappings['havefun']:
            logging.warning("警告：没有找到有效的HaveFun文件映射")
    
    if banner_valid:
        mappings['banner'] = get_banner_file_mapping(banner_path)
        if not mappings['banner']:
            logging.warning("警告：没有找到有效的Banner文件映射")
    
    try:
        with pd.ExcelFile(excel_path) as xls:
            logging.info(f"Excel文件包含的表单: {', '.join(xls.sheet_names)}")
            
            total_stats = {'have_fun': {'updated': 0, 'not_found': 0, 'processed': False},
                          'product_rec': {'updated': 0, 'not_found': 0, 'processed': False}}
            updated_sheets = {}
            
            # 处理【2 Have Fun】sheet
            if '2 Have Fun' in xls.sheet_names and havefun_valid:
                logging.info("开始处理【2 Have Fun】sheet...")
                df_havefun = pd.read_excel(xls, sheet_name='2 Have Fun')
                df_havefun, stats = process_havefun_sheet(df_havefun, base_url, 
                                                        mappings.get('havefun', {}), 
                                                        mappings.get('cover_page_url', {}))
                updated_sheets['2 Have Fun'] = df_havefun
                total_stats['have_fun'] = {**stats, 'processed': True}
            
            # 处理【1 Product Rec】sheet
            if '1 Product Rec' in xls.sheet_names and banner_valid:
                logging.info("开始处理【1 Product Rec】sheet...")
                df_product = pd.read_excel(xls, sheet_name='1 Product Rec')
                df_product, stats = process_product_sheet(df_product, base_url, mappings.get('banner', {}))
                updated_sheets['1 Product Rec'] = df_product
                total_stats['product_rec'] = {**stats, 'processed': True}
            
            if not updated_sheets:
                logging.warning("没有找到可处理的sheet或对应的目录，退出操作。")
                return
            
            # 保存文件
            save_excel_file(excel_path, updated_sheets)
            
            elapsed_time = time.time() - start_time
            logging.info(f"更新完成！用时: {elapsed_time:.2f}秒")
            
            # 输出统计信息
            logging.info("=== 处理统计 ===")
            total_updated = sum(stats['updated'] for stats in total_stats.values() if stats['processed'])
            total_not_found = sum(stats['not_found'] for stats in total_stats.values() if stats['processed'])
            
            for sheet_type, stats in total_stats.items():
                if stats['processed']:
                    sheet_name = "【2 Have Fun】" if sheet_type == 'have_fun' else "【1 Product Rec】"
                    logging.info(f"{sheet_name}: 更新{stats['updated']}条，未找到{stats['not_found']}条")
            
            logging.info(f"总计: 更新{total_updated}条记录，{total_not_found}条记录未找到对应文件")
    
    except Exception as e:
        logging.error(f"更新Excel时出错: {e}")


# 保持向后兼容性的函数别名
def validate_havefun_files(have_fun_path):
    return validate_directory(have_fun_path, "HaveFun目录")

def validate_banner_files(banner_path):
    return validate_directory(banner_path, "Banner目录")

def get_file_paths_mapping(have_fun_path):
    return get_havefun_file_mapping(have_fun_path)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='更新Excel文件中的URL')
    parser.add_argument('--excel', default='3month_data.xlsx', help='Excel文件路径')
    parser.add_argument('--url', required=True, help='基础URL，如https://xxxx.cloudfront.net/explore')
    parser.add_argument('--havefun', default='HaveFun', help='HaveFun目录路径')
    parser.add_argument('--banner', help='Banner目录路径（默认为HaveFun目录同级的Banner目录）')
    parser.add_argument('--backup', action='store_true', help='备份原始Excel文件')
    parser.add_argument('--log', help='日志文件路径，不提供则只输出到控制台')
    parser.add_argument('--debug', action='store_true', help='启用调试模式，显示更详细的日志')
    
    args = parser.parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logging(args.log, log_level)
    
    logging.info("==== URL更新工具 ====")
    logging.info(f"开始运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建备份
    if args.backup:
        import shutil
        backup_path = args.excel.replace('.xlsx', f'_backup_{pd.Timestamp.now().strftime("%Y%m%d%H%M%S")}.xlsx')
        try:
            shutil.copy2(args.excel, backup_path)
            logging.info(f"已创建备份: {backup_path}")
        except Exception as e:
            logging.error(f"创建备份失败: {e}")
    
    try:
        banner_path = args.banner or os.path.join(os.path.dirname(args.havefun), 'Banner')
        update_excel_urls(args.excel, args.url, args.havefun, banner_path)
        logging.info("==== 程序执行完毕 ====")
    except Exception as e:
        logging.error(f"程序执行过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()